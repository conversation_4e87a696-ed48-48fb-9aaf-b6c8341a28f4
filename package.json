{"name": "understandlegacycode.com", "version": "1.0.0", "private": true, "description": "Change Messy Software Without Breaking It", "keywords": ["legacy code", "technical debt", "refactoring", "documentation"], "homepage": "https://github.com/nicoespeon/understandlegacycode.com#readme", "bugs": {"url": "https://github.com/nicoespeon/understandlegacycode.com/issues"}, "repository": {"type": "git", "url": "git+https://github.com/nicoespeon/understandlegacycode.com.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "main": "n/a", "scripts": {"build": "astro build", "dev": "astro dev", "prepare": "husky", "preview": "astro preview", "start": "astro dev"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "dependencies": {"@fontsource/montserrat": "5.2.6", "@tailwindcss/vite": "4.1.11", "accessible-astro-components": "4.1.3"}, "devDependencies": {"@astrojs/mdx": "4.3.3", "@astrojs/partytown": "2.1.4", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.32.0", "@iconify-json/lucide": "1.2.60", "@typescript-eslint/eslint-plugin": "8.39.0", "@typescript-eslint/parser": "8.39.0", "astro": "5.12.8", "astro-compress": "2.3.8", "astro-eslint-parser": "1.2.2", "astro-icon": "1.1.5", "eslint": "9.32.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-astro": "1.3.1", "eslint-plugin-jsx-a11y": "6.10.2", "globals": "16.3.0", "husky": "9.1.7", "lint-staged": "16.1.4", "prettier": "3.6.2", "prettier-plugin-astro": "0.14.1", "prettier-plugin-css-order": "2.1.2", "prettier-plugin-curly": "0.3.2", "prettier-plugin-packagejson": "2.5.19", "prettier-plugin-sh": "0.18.0", "prettier-plugin-tailwindcss": "0.6.14", "pretty-quick": "4.2.2", "sanitize-html": "2.17.0", "sass": "1.89.2", "svgo": "3.3.2", "tailwindcss": "4.1.11"}, "engines": {"node": "~23.10.0"}}