{"dependencies": {"@mdx-js/mdx": "^1.5.1", "@mdx-js/react": "^1.5.1", "babel-plugin-styled-components": "^1.10.0", "book-cover-3d": "1.1.2", "color-hash": "1.0.3", "copee": "^1.0.6", "netlify-cms-app": "^2.9.1", "react": "^16.8.6", "react-dom": "^16.8.6", "react-helmet": "^5.2.0", "react-social-sharing": "3.0.1", "react-switch": "^5.0.0", "react-typography": "^0.16.19", "styled-components": "^4.2.0", "typeface-merriweather": "0.0.72", "typeface-montserrat": "0.0.54", "typography": "^0.16.19", "typography-theme-wordpress-2016": "^0.16.19"}}