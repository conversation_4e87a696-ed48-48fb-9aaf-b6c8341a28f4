backend:
  name: github
  repo: nicoespeon/understandlegacycode.com

media_folder: static/assets
public_folder: /assets
show_preview_links: true
publish_mode: editorial_workflow

collections:
  - name: blog
    label: Blog
    folder: content/blog
    preview_path: "blog/{{slug}}"
    create: true
    fields:
      - { name: title, label: Title }
      - { name: date, label: Date, widget: datetime, dateFormat: "YYYY-MM-DD" }
      - { name: description, label: Description, required: false }
      - { name: body, label: Body, widget: markdown }
