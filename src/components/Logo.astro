---
import { Image } from "astro:assets"
import logo from "../assets/img/logo.svg"

/**
 * Logo Component
 *
 * @description Logo description
 */
interface Props {
  /**
   * Whether to load the image eagerly or lazily
   * @default 'eager'
   */
  loading?: "eager" | "lazy"
}

const { loading = "eager" }: Props = Astro.props
---

<a href="/" class="flex items-center gap-2 !no-underline">
  <Image src={logo} alt="Accessible Astro Logo" width="47" height="37" loading={loading} />
  <span class="font-bold">Accessible Astro</span>
</a>

<style lang="scss">
  a {
    color: var(--foreground-color);
  }
</style>
