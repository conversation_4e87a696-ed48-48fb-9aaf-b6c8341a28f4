---
import { Icon } from "astro-icon/components"

/**
 * Feature Component
 *
 * @description A component that displays a feature with an icon, title, and description
 */
interface Props {
  /**
   * The icon to display
   */
  icon: string
  /**
   * The title to display
   */
  title: string
}

const { icon = "mdi:rocket", title = "Title" }: Props = Astro.props
---

<div class="feature flex flex-col gap-4">
  <Icon name={icon} />
  <div class="content">
    <h3 class="text-2xl font-bold">{title}</h3>
    <p>
      <slot>Lorem ipsum dolor sit amet consectetur adipisicing elit. Hic, corporis.</slot>
    </p>
  </div>
</div>

<style lang="scss">
  @use "../assets/scss/base/breakpoint" as *;

  .feature {
    position: relative;
    padding: var(--space-l);
    inline-size: calc(100% - var(--space-l));

    > * {
      position: relative;
      z-index: 2;
    }

    @include breakpoint(xl) {
      inline-size: 100%;
    }

    &::before,
    &::after {
      position: absolute;
      content: "";
    }

    &::before {
      z-index: 1;
      inset: 0;
      box-shadow: 0 0 0 6px var(--color-neutral-100);
      border: 3px solid var(--color-neutral-700);
      border-radius: var(--radius-l);
      background-color: var(--color-neutral-100);
    }

    &::after {
      z-index: 0;
      inset: var(--space-m) -0.85rem -0.85rem 1rem;
      border-radius: var(--radius-l);
      background-color: var(--link-color);
    }
  }

  :global(.feature [data-icon]) {
    inline-size: 4rem;
    block-size: auto;
    color: var(--link-color);
  }

  :global(.darkmode .feature::before) {
    box-shadow: 0 0 0 6px var(--color-neutral-900);
    background-color: var(--color-neutral-900);
  }
</style>
