---
import { Icon } from "astro-icon/components"

/**
 * ExternalLink Component
 *
 * @description ExternalLink description
 */
interface Props {
  /**
   * Additional classes to apply to the ExternalLink
   */
  class?: string
  /**
   * The URL to link to
   */
  href: string
}

const { class: className, href } = Astro.props
---

<a href={href} class:list={[className, "external-link"]} target="_blank" rel="noopener noreferrer">
  <slot />
  <Icon name="lucide:external-link" />
  <span class="sr-only">Opens in a new tab</span>
</a>

<style>
  .external-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-3xs);
  }
</style>
