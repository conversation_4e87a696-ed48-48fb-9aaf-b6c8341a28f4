---
import { Icon } from "astro-icon/components"

/**
 * CallToAction Component
 *
 * @description A prominent call-to-action section with a title and link
 */
interface Props {
  /**
   * The title text to display
   * @default "Get this theme on GitHub"
   */
  title?: string
  /**
   * The URL the button should link to
   * @default "https://github.com/incluud/accessible-astro-starter"
   */
  link?: string
  /**
   * The text to display on the button
   * @default "Get Started"
   */
  linkText?: string
}

const {
  title = "Get this theme on GitHub",
  link = "https://github.com/incluud/accessible-astro-starter",
  linkText = "Use this theme",
} = Astro.props
---

<div class="container">
  <div class="call-to-action mt-24 mb-32 flex flex-col items-center gap-12 rounded-xl px-12 py-20 md:px-24">
    <h2 class="text-center text-3xl md:text-6xl">{title}</h2>
    <a href={link} class="text-center text-2xl">
      {linkText}
      <Icon name="lucide:arrow-right" />
    </a>
  </div>
</div>

<style lang="scss">
  @use "../assets/scss/base/mixins" as *;

  .call-to-action {
    position: relative;
    background-image: linear-gradient(
      315deg,
      light-dark(var(--color-primary-100), var(--color-secondary-100)) 25%,
      light-dark(var(--color-secondary-100), var(--color-primary-200))
    );

    color: var(--color-neutral-900);
  }

  a,
  a:visited {
    display: flex;
    align-items: center;
    gap: var(--space-2xs);
    border: 3px solid var(--color-neutral-900);
    border-radius: var(--radius-s);
    padding: var(--space-s);
    color: var(--color-neutral-900);
    font-weight: bold;
    text-decoration: none;

    @include text-decoration(transparent, currentColor);

    [data-icon] {
      transition: translate var(--animation-speed-fast) var(--cubic-bezier);
    }

    &:where(:hover, :focus) {
      background-color: var(--color-neutral-900);
      color: var(--color-neutral-100);
      text-decoration: underline;

      [data-icon] {
        translate: 5px 0;
      }
    }
  }
</style>
