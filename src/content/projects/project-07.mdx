---
title: SmartHome Access
author: <PERSON>
description: An inclusive smart home automation system that enables people with mobility impairments to control their entire home environment independently
---

## Project Overview

SmartHome Access revolutionizes home automation by making it truly accessible for people with mobility impairments. The system combines voice control, eye tracking, and adaptive switches to provide multiple ways of controlling home environments, from lighting and temperature to doors and appliances.

import BreakoutImage from "@components/BreakoutImage.astro"
import { Image } from "astro:assets"

<BreakoutImage src="/projects/project-image-5.png" />

## Technical Challenges

The biggest hurdle was creating a reliable multi-modal control system that could adapt to different types of mobility impairments. We developed a flexible control interface that can be customized to each user's capabilities, from eye tracking to sip-and-puff controls, while maintaining responsive and reliable operation.

## Project Gallery

<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
  <Image
    src="/projects/project-image-4.png"
    alt="SmartHome Access control dashboard with multiple input methods"
    width={1200}
    height={600}
    class="h-[250px] w-full rounded-lg object-cover"
  />
  <Image
    src="/projects/project-image-5.png"
    alt="Eye tracking interface for home control"
    width={1200}
    height={600}
    class="h-[250px] w-full rounded-lg object-cover"
  />
  <Image
    src="/projects/project-image-6.png"
    alt="Adaptive switch modules for custom control layouts"
    width={1200}
    height={600}
    class="h-[250px] w-full rounded-lg object-cover"
  />
  <Image
    src="/projects/project-image-2.png"
    alt="Smart home environment controlled by the system"
    width={1200}
    height={600}
    class="h-[250px] w-full rounded-lg object-cover"
  />
</div>

## Testimonial

"SmartHome Access has given me back my independence at home. I can control everything in my environment without assistance, and the system adapts perfectly to my changing needs throughout the day."

- Michael Torres, Accessibility Advocate

<BreakoutImage src="/projects/project-image-3.png" />

Expanding into predictive automation and developing more sophisticated environmental control interfaces.
