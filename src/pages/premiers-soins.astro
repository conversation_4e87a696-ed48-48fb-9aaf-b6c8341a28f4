---
import CTA from "@components/CTA.astro"
import DefaultLayout from "@layouts/DefaultLayout.astro"

// This is the French version of the First Aid Kit landing page
---

<DefaultLayout title="Legacy Code: Premiers Soins" description="Secourez votre Legacy rapidement et sereinement.">
  <div class="premiers-soins-page">
    <!-- Top bar -->
    <div class="top-bar"></div>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-text">
          <h1>
            Secourez votre Legacy <em>rapidement et sereinement.</em>
            <span role="img" aria-label="Rescue Helmet">⛑️</span>
          </h1>
          <p>
            Apprenez les techniques pour refactor du code non testé et mal documenté, tout en livrant continuellement de
            la valeur à vos utilisateurs.
          </p>
          <p>
            Inscrivez-vous à ma newsletter et recevez{" "}
            <strong>un chapitre gratuit</strong>, extrait de mon guide, sur les Refactorings Incrémentaux qui vous
            permettent de vous arrêter et de livrer à tout instant.
          </p>
          <CTA variant="first-aid-kit" uid="45bef52aca" />
          <p>
            Ou bien <a href="#buy">achetez le guide dès maintenant</a> si vous êtes déjà convaincus !
          </p>
        </div>
        <div class="hero-book">
          <div class="book-container">
            <div class="book">
              <img src="/assets/premiers-soins-cover.png" alt="Legacy Code: Premiers Soins" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Content Section -->
    <section class="content-section">
      <div class="content">
        <h2 class="quote">J'aimerais avoir assez de temps pour refactor ce code !</h2>
        <p>
          Chaque semaine c'est la même histoire : vous <em>devez</em> toucher à ce code. Pour fixer un bug, ou pour changer
          un comportement, voire peut-être pour ajouter une toute nouvelle fonctionnalité.
        </p>
        <p>Mais ce n'est pas vous qui avez écrit ce code !</p>
        <p>
          C'est un bordel sans nom et mal documenté. Vous aimeriez bien refactor le code avant de le changer.{" "}
          <strong>Mais il n'y a aucun test !</strong> Et donc vous n'avez pas trop envie de vous risquer à casser quelque
          chose en le refactorant… Vous vous êtes déjà fait avoir une fois, pas deux !
        </p>
        <p>
          Alors oui, vous <em>pourriez</em> commencer par écrire les tests. Mais vous n'avez pas le temps ! Vous avez déjà
          trop de choses à faire dans le peu de temps qui vous a été imposé…
        </p>
        <p>
          Et vous y voilà donc : vous êtes bloqué, parce que vous ne savez pas par quel bout le prendre. Devriez-vous
          commencer par nettoyer le code, quitte à risquer la deadline ? Et comment allez-vous faire pour écrire des
          tests sur du code qui n'a clairement pas été écrit pour être testé ?
        </p>

        <h2 class="quote">Quand je commence à refactor un bout, y'a tout qui vient !</h2>
        <p>Vous avez déjà essayé de nettoyer le code, mais tout est complètement emmêlé !</p>
        <p>
          Vous avez fait des progrès mais <em>plus rien ne fonctionne</em>. Vous ne pouvez donc pas vous arrêter
          maintenant… sauf qu'il est déjà 21h et votre famille vous attend !
          <span role="img" aria-label="Angry face">😡</span>
        </p>
        <p>Vous connaissez la suite…</p>
        <p>
          Vous n'essayez même plus de comprendre ce chaos. Vous vous contentez de faire en sorte que Ça Marche©. C'est
          sale. Ce n'est pas si mal non plus. Vous avez croisé bien pire dans ce tas de code. Vous faites le boulot,
          quitte à aggraver la situation. Il faut livrer.
        </p>
        <p>
          Vous aimeriez bien pouvoir tout recommencer à zéro. Mais ce serait difficile à négocier. <strong
            >Pas de temps, ni d'argent</strong
          > pour ce genre de projet.
        </p>
        <p>
          Vous n'êtes pas très fier de cette base de code que vous maintenez. Mais bon, vous vous y habituez. Les
          clients demandent toujours plus de fonctionnalités, et vos collègues n'ont pas l'air de s'intéresser à la
          qualité du code. Vous avez l'impression de faire votre travail. Mais vous savez que vous pourriez faire mieux.
        </p>
        <p>
          Cela dit, votre chef ne comprend pas <em>pourquoi</em> ça vous prend si longtemps de faire les changements demandés.
          Vous avez déjà essayé de lui expliquer, mais les deadlines sont toujours trop courtes et la priorité est toujours
          donnée à la livraison de nouvelles fonctionnalités. Tout le monde se fiche bien que vos yeux saignent en lisant
          ce code !
        </p>
        <p>
          C'est fatigant !
          <span role="img" aria-label="Tired face">😫</span>
        </p>

        <h2>Si vous pouviez au moins arrêter l'hémorragie…</h2>
        <p>
          Vous savez ce qui serait chouette ? Travailler avec du code propre et bien testé. Pas de mauvaise surprise,
          facile à modifier. Le rêve… That would be a breeze to work with…
        </p>
        <p>
          Mais votre base de code à vous, c'est un champ de mine. Comment pourriez-vous espérer déminer des ANNÉES de
          Dette Technique ?!
        </p>
        <p>Et si je vous disais qu'il existe des manières de refactor le code ET de respecter les deadlines ?</p>
        <p>
          Imaginez que vous puissiez refactor le code tout en livrant des fonctionnalités. Imaginez que vous amélioriez
          l'état du code au fur et à mesure que vous revenez dessus, pour en faire un système facile à tester et à
          maintenir !
          <span role="img" aria-label="Seed">🌱</span>
        </p>
        <p>
          <strong>Si vous connaissez les bons gestes</strong>, vous pouvez apporter les premiers soins à votre code et
          arrêter le massacre. À quel point seriez-vous fier de vous et soulagé de pouvoir redonner des couleurs à votre
          code ?
        </p>
        <p>
          Bien sûr, le système que vous maintenez en ce moment est rempli de problèmes. Il a l'air de fonctionner, mais
          il ne survit vraiment qu'à travers des hacks et des patchs plus ou moins tordus. Parfois, vous vous demandez
          si ce ne serait pas plus simple de tout recommencer à zéro…
        </p>
        <p>Mais qu'en serait-il si vous découvriez des techniques qui vous permettraient de le sauver ?</p>

        <h2>Imaginez pouvoir nettoyer votre Code Legacy à chaque fois que vous y touchez.</h2>
        <p>
          Peu importe l'état de votre code, vous saurez toujours par où commencer. En appliquant constamment les bonnes
          techniques, vous pourrez arrêter le carnage et{" "}
          <strong>éviter une refonte très coûteuse</strong>.
        </p>
        <p>Mais surtout, vous continuerez de corriger des bugs et d'ajouter des fonctionnalités sans arrêt.</p>
        <p>
          <strong>
            Vous n'avez pas à demander la permission de refactor quand vous êtes capable de le faire à la volée !
          </strong>
        </p>
        <p>
          Le Refactoring deviendra une deuxième nature pour vous. Vos réflexes vous permettront de nettoyer n'importe
          quel Legacy plus rapidement que n'importe lequel de vos collègues ! Vous répondrez constamment aux attentes de
          vos clients et inspirerez vos pairs.
        </p>
        <p>
          En fait, vous pouvez commencer à améliorer cette base de code la prochaine fois que vous y touchez.
          <span role="img" aria-label="Sparkles">✨</span>
        </p>
        <p>
          Quand les deadlines sont serrées, il est risqué d'essayer de refactor le code… à moins de savoir <u
            >exactement</u
          > ce que vous faites.
        </p>

        <h2>
          Grâce à ce guide de Premiers Soins, vous allez apprendre à refactor sans risque et <em>rapidement</em>.
        </h2>
        <p>
          J'ai collecté pour vous un ensemble de techniques qui vous aideront à reprendre le contrôle de votre code
          Legacy. Ce sont les trucs et astuces qui fonctionnent le mieux pour moi. Je les utilise quand je travaille sur
          des projets en production qui manquent (évidemment) de tests et de docs (ça vous semble familier ?).
        </p>
        <p>Ces 14 mouvements vous aideront à :</p>
        <ul class="benefits-list">
          <li>optimiser votre travail pour avoir le maximum d'impact</li>
          <li>identifier <strong>ce qui rend le code difficile à tester</strong></li>
          <li><strong>rapidement installer un filet de sécurité</strong> sur le code que vous devez toucher</li>
          <li>améliorer la qualité du code <strong>progressivement</strong></li>
          <li>livrer à chaque instant !</li>
        </ul>
      </div>
    </section>
  </div>
</DefaultLayout>

<style>
  .premiers-soins-page {
    color: rgb(12, 30, 41);
    font-family:
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      Segoe UI,
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      Droid Sans,
      Helvetica Neue,
      Fira Sans,
      sans-serif;
    -webkit-font-smoothing: antialiased;
  }

  .premiers-soins-page a {
    transition: box-shadow 0.2s;
    box-shadow: none;
    background-image: linear-gradient(180deg, transparent 70%, hsla(354.6, 85.4%, 51.8%, 0.3) 0);
    color: rgb(12, 30, 41);
    font-weight: 600;
    text-decoration: none;
  }

  .premiers-soins-page a:hover,
  .premiers-soins-page a:focus,
  .premiers-soins-page a:active {
    box-shadow: inset 0 -1.3em 0 hsla(354.6, 85.4%, 51.8%, 0.3);
    background: 0 0;
  }

  .premiers-soins-page h1,
  .premiers-soins-page h2,
  .premiers-soins-page h3,
  .premiers-soins-page h4 {
    font-family: Montserrat, sans-serif;
  }

  .premiers-soins-page h1 em,
  .premiers-soins-page h2 em,
  .premiers-soins-page .highlight {
    color: hsla(354.6, 85.4%, 51.8%, 1);
    font-style: normal;
  }

  .top-bar {
    background-color: hsla(354.6, 85.4%, 51.8%, 1);
    height: 0.5rem;
  }

  .hero-section {
    background-color: #f3f7f9;
    padding: 5rem 1rem;
  }

  @media (min-width: 768px) {
    .hero-section {
      padding: 5rem 3rem;
    }
  }

  .hero-container {
    display: flex;
    flex-direction: column;
    margin: auto;
    max-width: 1280px;
  }

  @media (min-width: 1100px) {
    .hero-container {
      flex-direction: row;
      place-content: space-between;
    }
  }

  .hero-text {
    flex-basis: 65%;
    margin-bottom: 4rem;
  }

  @media (min-width: 1100px) {
    .hero-text {
      margin-bottom: 0;
    }
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  @media (min-width: 1100px) {
    .hero-text h1 {
      font-size: 3rem;
    }
  }

  .hero-text p {
    color: rgb(78, 97, 108);
    font-weight: 400;
    font-size: 1.5rem;
    line-height: 2.5rem;
  }

  .hero-book {
    flex-basis: 30%;
  }

  .book-container {
    perspective: 600px;
  }

  .book {
    transform: rotateY(30deg);
    transform-style: preserve-3d;
    transition: transform 1s;
  }

  .book:hover {
    transform: rotateY(0deg);
  }

  .book img {
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
    border-radius: 2px;
    width: 300px;
    height: 480px;
  }

  .content-section {
    padding: 2rem;
  }

  .content {
    margin: auto;
    max-width: 768px;
    font-size: 1rem;
  }

  @media (min-width: 768px) {
    .content {
      padding-right: 0;
      padding-left: 0;
      font-size: 1.25rem;
    }
  }

  .quote {
    position: relative;
    font-style: italic;
  }

  .quote::before {
    display: block;
    position: absolute;
    top: -30px;
    left: -60px;
    opacity: 0.75;
    z-index: -1;
    background-image: url("data:image/svg+xml,%3Csvg width='72' height='72' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13 14.725c0-5.141 3.892-10.519 10-11.725l.984 2.126c-2.215.835-4.163 3.742-4.38 5.746 2.491.392 4.396 2.547 4.396 5.149 0 3.182-2.584 4.979-5.199 4.979-3.015 0-5.801-2.305-5.801-6.275zm-13 0c0-5.141 3.892-10.519 10-11.725l.984 2.126c-2.215.835-4.163 3.742-4.38 5.746 2.491.392 4.396 2.547 4.396 5.149 0 3.182-2.584 4.979-5.199 4.979-3.015 0-5.801-2.305-5.801-6.275z' fill='%23ed1b2e' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
    width: 72px;
    height: 72px;
    content: "";
  }

  .benefits-list {
    margin-top: 0;
    padding-left: 0;
    list-style-type: none;
  }

  .benefits-list li {
    position: relative;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
    padding-left: 26px;
    color: rgb(78, 97, 108);
  }

  .benefits-list li::before {
    position: absolute;
    top: 8px;
    left: 0;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj4KICA8ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNDNkY2RDUiLz4KICAgIDxwYXRoIGZpbGw9IiMzOEExNjkiIGZpbGwtcnVsZT0ibm9uemVybyIgZD0iTTEuNzA0NDU1NDUsNC41ODg5MTA4OSBMMy42MDczMjY3Myw2Ljc4OTIwNzkyIEw5LjE2NzMyNjczLDAuMTg4NDE1ODQyIEM5LjU4MzQ3NTI1LC0wLjI1NzUxNDg1MSAxMC4yMzc4MjE4LDAuMjE4MTk2MDQgOS45MTA2NzMyNywwLjcyMzY4MzE2OCBMNC40Mzk5ODAyLDkuMDc4NDM1NjQgQzQuMDIzODMxNjgsOS42MTM3MDI5NyAzLjQ1ODc3MjI4LDkuNjczMjY3MzMgMi45ODMwNDk1LDkuMTM3OTk2MDQgTDAuMjE3NzAyOTcsNS44Mzc3OTgwMiBDLTAuMzE3NTY0MzU2LDUuMDY0NjY5MzEgMS4wNTAzOTYwNCwzLjk2NDcyODcxIDEuNzA0NDM1NjQsNC41ODg5ODYxNCBMMS43MDQ0NTU0NSw0LjU4ODkxMDg5IFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUgNSkiLz4KICA8L2c+Cjwvc3ZnPgo=");
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
    content: "";
  }
</style>
