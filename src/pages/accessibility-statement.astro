---
import DefaultLayout from "@layouts/DefaultLayout.astro"
import PageHeader from "@components/PageHeader.astro"
---

<DefaultLayout title="Accessibility statement">
  <PageHeader
    title="Accessibility statement"
    subtitle="This document outlines the accessibility features and support provided by our website. Be sure to fill in the details for your own website and check the WCAG 2.2 AA guidelines."
    bgType="bordered"
  />
  <section class="narrow container my-16">
    <div class="space-content">
      <h2>Introduction</h2>
      <p>
        This accessibility statement applies to <strong>[website URL]</strong>. At <strong>[Organization Name]</strong>,
        we are committed to ensuring digital accessibility for people of all abilities. We believe that everyone should
        have equal access to information and functionality on the web, regardless of physical or technological
        limitations. Accessibility is a core value in our commitment to providing an inclusive user experience.
      </p>

      <h2>WCAG compliance</h2>
      <p>
        Our website is designed to conform to the Web Content Accessibility Guidelines (WCAG) 2.2, Level AA. These
        guidelines explain how to make web content more accessible to people with a wide array of disabilities,
        including visual, auditory, physical, speech, cognitive, language, learning, and neurological disabilities.
      </p>

      <h2>Current status</h2>
      <p>
        Our website has been internally reviewed for accessibility, and the following accessibility features have been
        implemented:
      </p>
      <ul class="list-inside list-disc">
        <li>Proper HTML language attributes for screen readers and assistive technologies</li>
        <li>Keyboard navigability for all interactive elements</li>
        <li>Sufficient color contrast for text and visual elements</li>
        <li>Logical focus order for keyboard navigation</li>
        <li>Proper form error identification and suggestions</li>
        <li>Skip links to bypass repetitive navigation</li>
        <li>Responsive design that works across different devices and screen sizes</li>
        <li>Text alternatives for non-text content</li>
        <li>ARIA attributes where appropriate to enhance accessibility</li>
      </ul>

      <h2>Areas for improvement</h2>
      <p>
        We recognize that there are areas requiring improvement, such as <strong>[specific features or elements]</strong
        >, and these have been included in our ongoing enhancement plan.
      </p>

      <h2>Feedback process</h2>
      <p>
        We welcome your feedback on the accessibility of our website. If you encounter any barriers or have suggestions
        for improvement, please help us by providing the following information:
      </p>
      <ul class="list-inside list-disc">
        <li><strong>URL:</strong> The web address of the content where you experienced the issue</li>
        <li><strong>Description:</strong> A clear description of the problem</li>
        <li><strong>Contact details:</strong> Your name and email address (optional)</li>
      </ul>
      <p>Please contact us at <strong>[email address]</strong> or <strong>[phone number]</strong>.</p>

      <h2>Improvement plan</h2>
      <p>
        We are actively working to improve the accessibility of our website through regular reviews and updates. Planned
        improvements are focused on <strong>[specific areas]</strong> and are scheduled to be completed by <strong
          >[specific deadline]</strong
        >.
      </p>

      <h2>Contact information</h2>
      <p>
        For further questions or comments regarding this accessibility statement or the accessibility of our website,
        please contact us through our <strong>[contact form/email address]</strong>.
      </p>

      <h2>Publication information</h2>
      <p>
        This accessibility statement was prepared on <strong>[date]</strong> and last updated on <strong>[date]</strong
        >. The website was last tested on
        <strong>[latest test date]</strong> against WCAG 2.2 AA guidelines.
      </p>
    </div>
  </section>
</DefaultLayout>
