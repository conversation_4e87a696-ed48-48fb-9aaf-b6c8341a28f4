---
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="Approval Tests"
  description="A technique to quickly put Legacy Code under tests, so you can refactor it"
  tag="approval tests"
>
  <h1>Approval Testing</h1>

  <blockquote>
    <p>I need to change this code, but it has no test!</p>
  </blockquote>

  <p>
    This is the title of a talk I gave at{" "}
    <a
      href="https://confoo.ca/en/yul2020/session/i-need-to-change-this-code-but-it-has-no-test"
      target="_blank"
      rel="noopener noreferrer"
    >
      ConFoo 2020
    </a>
    . Can you relate?
  </p>

  <p>I present a technique known under different names:</p>
  <ul>
    <li>Approval Tests</li>
    <li>Characterization Tests</li>
    <li>Golden Master</li>
    <li>Snapshot Tests</li>
    <li>Locking Tests</li>
    <li>Regression Tests</li>
  </ul>

  <p>
    The purpose of the technique is to{" "}
    <strong>capture the existing behavior of system</strong> to create{" "}
    <strong>regression tests</strong> that will tell you if it changes.
  </p>

  <p>
    It's used to <strong>quickly put Legacy Code under tests</strong> so you can refactor it safely.
  </p>

  <h2>
    <span role="img" aria-label="open book">📖</span>
    &nbsp;Synopsis
  </h2>

  <p style="text-align: center;">
    <img src="/assets/confoo-2019.jpg" alt="" />
  </p>

  <p>
    Have you ever felt helpless, facing some cryptic code you had to change? You know, the kind of code that has no test
    to tell you if something breaks…
  </p>

  <p>This is a scenario I frequently faced. And I learned better ways to work with such Legacy Code.</p>

  <p>
    In this session, I'll show you how to write tests on existing code, even when you don't understand what it does.
    You'll learn how to modify this code and know that nothing broke when you're done.
  </p>

  <h2>
    <span role="img" aria-label="toolbox">🧰</span>
    &nbsp;Resources
  </h2>

  <ul>
    <li>
      <a
        href="https://github.com/nicoespeon/talk-how-to-change-untested-code/"
        target="_blank"
        rel="noopener noreferrer"
      >
        Slides of my talk
      </a>
    </li>
    <li>
      <a href="https://jestjs.io/" target="_blank" rel="noopener noreferrer"> Jest, the JavaScript test runner </a>{" "}
      I used in the presentation
    </li>
    <li>
      <a href="https://www.npmjs.com/package/jest-extended-snapshot" target="_blank" rel="noopener noreferrer">
        jest-extended-snapshot
      </a>{" "}
      which provides Jest matchers to write Approval Tests
    </li>
    <li>
      <a href="https://approvaltests.com/" target="_blank" rel="noopener noreferrer">approvaltests.com</a>{" "}
      which contains resources to do the same if you're not using JavaScript and Jest
    </li>
    <li>
      <a href="https://github.com/nicoespeon/kata-gilded-rose-js" target="_blank" rel="noopener noreferrer">
        My Gilded Rose kata starter
      </a>{" "}
      in JavaScript. You can find the Approval Tests solution in the `approval-testing` branch.
    </li>
    <li>
      <a href="https://github.com/emilybache/GildedRose-Refactoring-Kata" target="_blank" rel="noopener noreferrer">
        Emily Bache's Gilded Rose kata starter
      </a>{" "}
      in many, many languages
    </li>
    <li>
      <a href="https://touca.io/" target="_blank" rel="noopener noreferrer">Touca</a> is a continuous regression testing
      tool that help you do that!
    </li>
  </ul>
</GuideLayout>
