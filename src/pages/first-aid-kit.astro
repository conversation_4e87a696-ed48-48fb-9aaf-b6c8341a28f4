---
import CTA from "@components/CTA.astro"
import DefaultLayout from "@layouts/DefaultLayout.astro"

// This is the English version of the First Aid Kit landing page
---

<DefaultLayout title="Legacy Code: First Aid Kit" description="Rescue your Legacy codebase quickly and safely.">
  <div class="first-aid-kit-page">
    <!-- Top bar -->
    <div class="top-bar"></div>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-text">
          <h1>
            Rescue your Legacy codebase <em>quickly and safely.</em>
            <span role="img" aria-label="Rescue Helmet">⛑️</span>
          </h1>
          <p>
            Learn how to refactor an existing codebase as you go using specific techniques to incrementally make it
            easier to maintain.
          </p>
          <p>
            Sign up for my newsletter to get{" "}
            <strong>a free chapter preview</strong> on performing incremental refactorings so you can stop and ship at any
            time.
          </p>
          <CTA variant="first-aid-kit" uid="45bef52aca" />
          <p>
            Or <a href="#buy">buy it now</a> if you're already convinced!
          </p>
        </div>
        <div class="hero-book">
          <div class="book-container">
            <div class="book">
              <img src="/assets/first-aid-kit-cover.png" alt="Legacy Code: First Aid Kit" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Content Section -->
    <section class="content-section">
      <div class="content">
        <h2 class="quote">I wish we had more time to refactor this code!</h2>
        <p>
          Every week it's the same story: you <em>have</em> to change that codebase. Maybe it's to fix a bug, to tweak some
          behavior, or to implement a new shiny feature.
        </p>
        <p>You didn't write that code!</p>
        <p>
          It's an undocumented tangled mess. You would love to refactor this code before you change it. <strong
            >But it has no test!</strong
          >{" "}
          Without tests, you're afraid to break something while you refactor—you got burned already, not twice!
        </p>
        <p>
          OK, so you <em>could</em> start by writing the tests. But you're worried that won't have enough time to meet "the
          deadline" that was imposed on you…
        </p>
        <p>
          And there you are: paralyzed because you don't know where to start. Should you begin a huge cleanup and risk
          the deadline? How can you add tests on a code that was definitely not written to be testable?!
        </p>

        <h2 class="quote">Wherever I start refactoring, it pulls the rest of the app!</h2>
        <p>You try to unwind the mess and the string just keeps on coming.</p>
        <p>
          It is getting better, but you can't stop because{" "}
          <em>it is not working yet</em>… but it's 9pm now, and your loved ones are mad at you!
          <span role="img" aria-label="Angry face">😡</span>
        </p>
        <p>You know what happens next…</p>
        <p>
          You stop trying to make sense out of this mayhem. You just Make It Work™. It's not pretty. It's not that bad.
          There are worse hacks in this spaghetti! You get the job done while making the code worse.
        </p>
        <p>
          You wish you could just start everything over, but you can't.{" "}
          <strong>No time, no budget.</strong>
        </p>
        <p>
          You are not proud of this code—how could you? But you're getting used to it. Clients are pressing you for
          "more features" and your colleagues don't seem to care.
        </p>
        <p>
          Your boss doesn't understand <em>why</em> it takes you longer and longer to finish tasks. You raised the point
          already, but deadlines are still too short and the focus is always on cost and time. No-one cares that your eyes
          bleed while reading the code!
        </p>
        <p>
          Exhausting!
          <span role="img" aria-label="Tired face">😫</span>
        </p>

        <h2>If you could at least stop the bleed…</h2>
        <p>You know what would be great? Working with clean, well-tested code. That would be a breeze to work with…</p>
        <p>
          But this codebase is a minefield. How could you stop making it worse when it would take YEARS to address the
          Technical Debt!?
        </p>
        <p>What if there was a way to refactor the code AND consistently meeting the deadlines?</p>
        <p>
          Imagine you could refactor as you go, steadily turning the codebase into a testable, easy-to-read,
          well-designed system!
          <span role="img" aria-label="Seed">🌱</span>
        </p>
        <p>
          <strong>If you know the moves</strong>, you can give your code first aid that will stop its hemorrhage. How
          proud and relieved would you feel as you put it back into a healthier shape?
        </p>
        <p>
          Sure, this system you're maintaining is broken everywhere. It seems to work, but it only really survives with
          heavy machinery and clever hacks. It looks like it would better to let it go and build a fresh one instead…
        </p>
        <p>But what if you knew the techniques to rescue it?</p>

        <h2>Imagine cleaning up Legacy Code as you go.</h2>
        <p>
          Regardless of the state of your codebase, you will always know where to start. Consistently applying the
          proper techniques, you can stop the carnage and <strong>avoid a costly rewrite</strong>.
        </p>
        <p>Most of all, you will keep shipping bug fixes and features as you go.</p>
        <p>
          <strong> You don't need to ask permission to refactor when you can do it on the fly! </strong>
        </p>
        <p>
          Refactoring would become second nature to you. Your reflexes will make you clean up Legacy Code in no time!
          You will consistently meet clients' expectations and inspire your peers.
        </p>
        <p>
          You can start making this codebase a better place the very next time you touch it.
          <span role="img" aria-label="Sparkles">✨</span>
        </p>
        <p>
          When you have short deadlines, trying to refactor the code is a risky move… unless you know <u>exactly</u> what
          you're doing.
        </p>

        <h2>
          Refactor code in <em>no time</em> with this First Aid Kit.
        </h2>
        <p>
          I've built a toolbox of techniques that will help you get your Legacy Code under control. These are the tricks
          that work the best for me when working with a real-life codebase, with missing tests and short
          deadlines—sounds familiar, right?
        </p>
        <p>These 14 moves will help you:</p>
        <ul class="benefits-list">
          <li>optimize your work to have the maximum impact</li>
          <li>identify <strong>what makes code difficult to test</strong></li>
          <li><strong>quickly put a safety net</strong> on the code you need to change</li>
          <li>improve code quality <strong>incrementally</strong></li>
          <li>ship at any time!</li>
        </ul>
      </div>
    </section>
  </div>
</DefaultLayout>

<style>
  .first-aid-kit-page {
    color: rgb(12, 30, 41);
    font-family:
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      Segoe UI,
      Roboto,
      Oxygen,
      Ubuntu,
      Cantarell,
      Droid Sans,
      Helvetica Neue,
      Fira Sans,
      sans-serif;
    -webkit-font-smoothing: antialiased;
  }

  .first-aid-kit-page a {
    transition: box-shadow 0.2s;
    box-shadow: none;
    background-image: linear-gradient(180deg, transparent 70%, hsla(354.6, 85.4%, 51.8%, 0.3) 0);
    color: rgb(12, 30, 41);
    font-weight: 600;
    text-decoration: none;
  }

  .first-aid-kit-page a:hover,
  .first-aid-kit-page a:focus,
  .first-aid-kit-page a:active {
    box-shadow: inset 0 -1.3em 0 hsla(354.6, 85.4%, 51.8%, 0.3);
    background: 0 0;
  }

  .first-aid-kit-page h1,
  .first-aid-kit-page h2,
  .first-aid-kit-page h3,
  .first-aid-kit-page h4 {
    font-family: Montserrat, sans-serif;
  }

  .first-aid-kit-page h1 em,
  .first-aid-kit-page h2 em,
  .first-aid-kit-page .highlight {
    color: hsla(354.6, 85.4%, 51.8%, 1);
    font-style: normal;
  }

  .top-bar {
    background-color: hsla(354.6, 85.4%, 51.8%, 1);
    height: 0.5rem;
  }

  .hero-section {
    background-color: #f3f7f9;
    padding: 5rem 1rem;
  }

  @media (min-width: 768px) {
    .hero-section {
      padding: 5rem 3rem;
    }
  }

  .hero-container {
    display: flex;
    flex-direction: column;
    margin: auto;
    max-width: 1280px;
  }

  @media (min-width: 1100px) {
    .hero-container {
      flex-direction: row;
      place-content: space-between;
    }
  }

  .hero-text {
    flex-basis: 65%;
    margin-bottom: 4rem;
  }

  @media (min-width: 1100px) {
    .hero-text {
      margin-bottom: 0;
    }
  }

  .hero-text h1 {
    font-size: 2.5rem;
  }

  @media (min-width: 1100px) {
    .hero-text h1 {
      font-size: 3rem;
    }
  }

  .hero-text p {
    color: rgb(78, 97, 108);
    font-weight: 400;
    font-size: 1.5rem;
    line-height: 2.5rem;
  }

  .hero-book {
    flex-basis: 30%;
  }

  .book-container {
    perspective: 600px;
  }

  .book {
    transform: rotateY(30deg);
    transform-style: preserve-3d;
    transition: transform 1s;
  }

  .book:hover {
    transform: rotateY(0deg);
  }

  .book img {
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
    border-radius: 2px;
    width: 300px;
    height: 480px;
  }

  .content-section {
    padding: 2rem;
  }

  .content {
    margin: auto;
    max-width: 768px;
    font-size: 1rem;
  }

  @media (min-width: 768px) {
    .content {
      padding-right: 0;
      padding-left: 0;
      font-size: 1.25rem;
    }
  }

  .quote {
    position: relative;
    font-style: italic;
  }

  .quote::before {
    display: block;
    position: absolute;
    top: -30px;
    left: -60px;
    opacity: 0.75;
    z-index: -1;
    background-image: url("data:image/svg+xml,%3Csvg width='72' height='72' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13 14.725c0-5.141 3.892-10.519 10-11.725l.984 2.126c-2.215.835-4.163 3.742-4.38 5.746 2.491.392 4.396 2.547 4.396 5.149 0 3.182-2.584 4.979-5.199 4.979-3.015 0-5.801-2.305-5.801-6.275zm-13 0c0-5.141 3.892-10.519 10-11.725l.984 2.126c-2.215.835-4.163 3.742-4.38 5.746 2.491.392 4.396 2.547 4.396 5.149 0 3.182-2.584 4.979-5.199 4.979-3.015 0-5.801-2.305-5.801-6.275z' fill='%23ed1b2e' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
    width: 72px;
    height: 72px;
    content: "";
  }

  .benefits-list {
    margin-top: 0;
    padding-left: 0;
    list-style-type: none;
  }

  .benefits-list li {
    position: relative;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
    padding-left: 26px;
    color: rgb(78, 97, 108);
  }

  .benefits-list li::before {
    position: absolute;
    top: 8px;
    left: 0;
    background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj4KICA8ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPgogICAgPGNpcmNsZSBjeD0iMTAiIGN5PSIxMCIgcj0iMTAiIGZpbGw9IiNDNkY2RDUiLz4KICAgIDxwYXRoIGZpbGw9IiMzOEExNjkiIGZpbGwtcnVsZT0ibm9uemVybyIgZD0iTTEuNzA0NDU1NDUsNC41ODg5MTA4OSBMMy42MDczMjY3Myw2Ljc4OTIwNzkyIEw5LjE2NzMyNjczLDAuMTg4NDE1ODQyIEM5LjU4MzQ3NTI1LC0wLjI1NzUxNDg1MSAxMC4yMzc4MjE4LDAuMjE4MTk2MDQgOS45MTA2NzMyNywwLjcyMzY4MzE2OCBMNC40Mzk5ODAyLDkuMDc4NDM1NjQgQzQuMDIzODMxNjgsOS42MTM3MDI5NyAzLjQ1ODc3MjI4LDkuNjczMjY3MzMgMi45ODMwNDk1LDkuMTM3OTk2MDQgTDAuMjE3NzAyOTcsNS44Mzc3OTgwMiBDLTAuMzE3NTY0MzU2LDUuMDY0NjY5MzEgMS4wNTAzOTYwNCwzLjk2NDcyODcxIDEuNzA0NDM1NjQsNC41ODg5ODYxNCBMMS43MDQ0NTU0NSw0LjU4ODkxMDg5IFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUgNSkiLz4KICA8L2c+Cjwvc3ZnPgo=");
    background-repeat: no-repeat;
    width: 20px;
    height: 20px;
    content: "";
  }
</style>
