---
import GuideLayout from "@layouts/GuideLayout.astro"
---

<GuideLayout
  title="Dependency Graphs"
  description="A technique to understand the structure of a codebase"
  tag="dependency graph"
>
  <h1>Draw Dependency Graphs</h1>

  <p>
    A dependency graph is a directed graph that represents the dependencies of several objects towards each other. There
    are different flavors of such graphs.
  </p>

  <p>
    The main question you need to answer is:{" "}
    <em>if I touch this, what else would be impacted?</em>
  </p>

  <p>
    You can generate these graphs with a pen and a paper. Automated tools may be helpful but are usually cluttered with
    noise. Don't underestimate the low-tech approach to get started!
  </p>

  <p>
    Techniques like the Mikado Method leverages such graphs. Concretely, it makes you discover the graph of <em
      >tasks</em
    > that needs to be completed before you can achieve your main goal.
  </p>

  <h2>
    <span role="img" aria-label="toolbox">🧰</span>
    &nbsp;Tools to draw them manually
  </h2>

  <ul>
    <li>
      <a href="https://mermaid-js.github.io/mermaid-live-editor/#/edit" target="_blank" rel="noopener noreferrer">
        Mermaid online editor
      </a>{" "}
      is really good
    </li>
    <li>
      <a href="https://playground.diagram.codes/d/graph" target="_blank" rel="noopener noreferrer">
        diagrams.code playground
      </a>{" "}
      is very smooth to use, but can feel more restricted
    </li>
    <li>
      <a href="https://www.lucidchart.com/" target="_blank" rel="noopener noreferrer">Lucidchart</a> might be helpful, although
      I recommend not spending too much energy in the layout—unless you are creating it for documentation purpose.
    </li>
  </ul>

  <h2>
    <span role="img" aria-label="toolbox">🤖</span>
    &nbsp;Tools to draw them automatically
  </h2>

  <ul>
    <li>
      <a href="https://github.com/glato/emerge" target="_blank" rel="noopener noreferrer">emerge</a>{" "}
      covers many languages—I'm contributing to this one!
    </li>
    <li>
      <a href="https://www.ndepend.com/" target="_blank" rel="noopener noreferrer">NDepend</a> for .NET developers
    </li>
    <li>
      <a href="https://sourcespy.com/" target="_blank" rel="noopener noreferrer">SourceSpy</a> for Java developers (it also
      allows creating manual diagrams)
    </li>
    <li>
      <a href="https://github.com/sensiolabs-de/deptrac" target="_blank" rel="noopener noreferrer"> deptrac </a>{" "}
      for PHP developers
    </li>
    <li>
      <a href="https://github.com/thebjorn/pydeps" target="_blank" rel="noopener noreferrer">pydeps</a>{" "}
      for Python developers
    </li>
    <li>
      <a href="https://github.com/emad-elsaid/rubrowser" target="_blank" rel="noopener noreferrer"> rubrowser </a>{" "}
      for Ruby developers
    </li>
    <li>
      <a href="https://github.com/pahen/madge" target="_blank" rel="noopener noreferrer">madge</a> for JavaScript developers
    </li>
    <li>
      <a href="https://github.com/sverweij/dependency-cruiser" target="_blank" rel="noopener noreferrer">
        dependency-cruiser
      </a>{" "}
      is also a great (and versatile) one for JavaScript developers
    </li>
    <li>
      <a href="https://www.sapling-extension.com/" target="_blank" rel="noopener noreferrer">Sapling</a>{" "}
      is useful if you work with React in VS Code
    </li>
  </ul>
</GuideLayout>
