---
import DefaultLayout from "@layouts/DefaultLayout.astro"
import PageHeader from "@components/PageHeader.astro"
import SocialShares from "@components/SocialShares.astro"
import { getCollection, render } from "astro:content"
import type { CollectionEntry } from "astro:content"

export async function getStaticPaths() {
  const projects = await getCollection("projects")

  const projectImages = [
    { src: "/projects/project-image-1.png" },
    { src: "/projects/project-image-2.png" },
    { src: "/projects/project-image-3.png" },
    { src: "/projects/project-image-4.png" },
    { src: "/projects/project-image-5.png" },
    { src: "/projects/project-image-6.png" },
  ]

  return projects.map((project, index) => {
    const projectWithImage = {
      ...project,
      featuredImage: projectImages[index % projectImages.length].src,
    }

    return {
      params: { project: project.id },
      props: { project: projectWithImage },
    }
  })
}

interface Props {
  project: CollectionEntry<"projects"> & { featuredImage: string }
}

const { project } = Astro.props
const { Content } = await render(project)

const author = {
  name: project.data.author,
  image: "/projects/project-image-1.png",
  bio: "Project Creator",
}
---

<DefaultLayout title={project.data.title} description={project.data.description} url={project.data.title}>
  <PageHeader
    title={project.data.title}
    subtitle={project.data.description}
    author={author}
    bgType="bordered"
    featuredImage={project.featuredImage}
  />
  <section class="my-12">
    <div class="narrow space-content container">
      <Content />
    </div>
  </section>
  <section class="my-12">
    <div class="narrow space-content container">
      <h2>Share this project</h2>
      <p>Like this project? Share it with your network!</p>
      <SocialShares />
    </div>
  </section>
</DefaultLayout>

<style lang="scss" is:global>
  .narrow {
    margin-inline: auto;
    max-width: 65ch;
  }
</style>
