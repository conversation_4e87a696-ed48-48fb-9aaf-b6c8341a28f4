---
import DefaultLayout from "@layouts/DefaultLayout.astro"
import PageHeader from "@components/PageHeader.astro"
import ColorContrastComponent from "@components/ColorContrast.astro"
---

<DefaultLayout title="Color Contrast Checker">
  <PageHeader
    title="Color Contrast Checker"
    subtitle="This tool helps you verify which colors in your theme's palette meet the WCAG contrast requirements for accessibility in both light and dark modes."
    bgType="neutral"
  />

  <section class="container my-16">
    <div class="space-content">
      <h2>Understanding Contrast Ratings</h2>
      <ul class="flex list-none flex-col gap-2">
        <li><span class="tag excellent">7.0+ AAA</span>Excellent contrast, exceeds WCAG AAA (7:1)</li>
        <li><span class="tag good">4.5+ AA</span>Good contrast, meets WCAG AA for normal text (4.5:1)</li>
        <li><span class="tag fair">3.0+ AA Large</span>Fair contrast, meets WCAG AA for large text only (3:1)</li>
        <li><span class="tag poor">Fail</span>Poor contrast, doesn't meet minimum WCAG requirements</li>
      </ul>
    </div>
  </section>

  <div class="container my-16">
    <ColorContrastComponent />
  </div>

  <style>
    .tag {
      display: inline-block;
      margin-inline-end: 0.5rem;
      border-radius: var(--radius-xs);
      padding: 0.2rem 0.5rem;
      font-weight: 700;
    }

    .excellent {
      background-color: rgb(9, 126, 87);
      color: white;
    }

    .good {
      background-color: rgb(41, 168, 126);
      color: black;
    }

    .fair {
      background-color: rgb(219, 141, 31);
      color: black;
    }

    .poor {
      background-color: rgb(221, 39, 54);
      color: white;
    }
  </style>
</DefaultLayout>
