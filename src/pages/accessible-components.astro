---
import DefaultLayout from "../layouts/DefaultLayout.astro"
import { Icon } from "astro-icon/components"
import PageHeader from "../components/PageHeader.astro"
import {
  Accordion,
  AccordionItem,
  Avatar,
  AvatarGroup,
  Badge,
  Breadcrumbs,
  BreadcrumbsItem,
  Card,
  DarkMode,
  Media,
  Modal,
  Notification,
  Pagination,
  Tabs,
  TabsList,
  TabsTab,
  TabsPanel,
  Video,
} from "accessible-astro-components"
---

<DefaultLayout title="Accessible components">
  <section class="my-12">
    <PageHeader
      title="Accessible components"
      subtitle="This theme has a lot of extra A11Y components provided by the <a href='https://github.com/incluud/accessible-astro-components'>Accessible Astro Components</a> NPM package to help you build accessible pages faster. To learn more about the components and how to use them, checkout our <a href='https://accessible-astro.incluud.dev/'>documentation website</a>. The theme itself ships with many other components you can find in the components folder."
      bgType="bordered"
    />
  </section>
  <section class="mt-32 mb-12">
    <div class="container">
      <div class="grid grid-cols-1 gap-32 md:grid-cols-2">
        <div class="space-content">
          <h2>Accordion (default)</h2>
          <Accordion>
            <AccordionItem title="First Item">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis <a href="#">sapiente obcaecati</a> magnam incidunt sit. Molestiae exercitationem quibusdam
                quod veritatis laboriosam est tenetur.
              </p>
              <a href="#">Tab to me!</a>
            </AccordionItem>
            <AccordionItem title="Second Item">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
            <AccordionItem title="Third Item">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
            <AccordionItem title="Fourth Item">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
            <AccordionItem title="Fifth Item">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
          </Accordion>
        </div>
        <div class="space-content">
          <h2>Accordion (chevron)</h2>
          <Accordion>
            <AccordionItem name="exclusive" title="First Item" variant="chevron">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis <a href="#">sapiente obcaecati</a> magnam incidunt sit. Molestiae exercitationem quibusdam
                quod veritatis laboriosam est tenetur.
              </p>
              <a href="#">Tab to me!</a>
            </AccordionItem>
            <AccordionItem name="exclusive" title="Second Item" variant="chevron">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
            <AccordionItem name="exclusive" title="Third Item" variant="chevron">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
            <AccordionItem name="exclusive" title="Fourth Item" variant="chevron">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
            <AccordionItem name="exclusive" title="Fifth Item" variant="chevron">
              <p>
                Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
                accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
                veritatis laboriosam est tenetur.
              </p>
            </AccordionItem>
          </Accordion>
        </div>
        <div class="space-content">
          <h2>Avatar</h2>
          <div class="flex-w flex gap-3">
            <Avatar initials="MT" label="Mark Teekman" />
            <Avatar initials="PP" label="Peter Padberg" type="info" shape="square" />
            <Avatar
              label="Astronout"
              img="https://images.unsplash.com/photo-1620428268482-cf1851a36764?auto=format&fit=crop&q=100&w=800"
            />
          </div>
          <Avatar title="Mark Teekman" subtitle="Developer & A11Y Expert" />
          <AvatarGroup>
            <Avatar size="sm" label="Developer" initials="DV" />
            <Avatar size="sm" type="info" label="Designer" initials="DS" />
            <Avatar size="sm" type="success" label="Product Manager" initials="PM" />
            <Avatar size="sm" type="warning" label="Marketing" initials="MK" />
            <Avatar size="sm" type="error" label="Marketing" initials="MK" />
          </AvatarGroup>
        </div>
        <div class="space-content">
          <h2>Badge</h2>
          <div class="flex flex-wrap gap-2">
            <Badge type="default">Default</Badge>
            <Badge type="info">Info</Badge>
            <Badge type="success">Success</Badge>
            <Badge type="warning">Warning</Badge>
            <Badge type="error">Error</Badge>
          </div>
        </div>
        <div class="space-content">
          <h2>Breadcrumbs</h2>
          <Breadcrumbs>
            <BreadcrumbsItem href="/" label="Home" />
            <BreadcrumbsItem href="/blog" label="Blog" />
            <BreadcrumbsItem currentPage={true} label="My blog post" />
          </Breadcrumbs>
          <Breadcrumbs>
            <BreadcrumbsItem href="/" label="Home" hasIcon>
              <Icon name="lucide:home" slot="icon" />
            </BreadcrumbsItem>
            <BreadcrumbsItem href="/docs" label="Documentation" />
            <BreadcrumbsItem label="Breadcrumbs" currentPage={true} />
          </Breadcrumbs>
          <Breadcrumbs>
            <BreadcrumbsItem href="/" label="Home">
              <span slot="separator" class="separator" aria-hidden="true">
                <Icon name="lucide:chevron-right" />
              </span>
            </BreadcrumbsItem>
            <BreadcrumbsItem href="/docs" label="Documentation">
              <span slot="separator" class="separator" aria-hidden="true">
                <Icon name="lucide:chevron-right" />
              </span>
            </BreadcrumbsItem>
            <BreadcrumbsItem label="Breadcrumbs" currentPage={true} />
          </Breadcrumbs>
          <Breadcrumbs>
            <BreadcrumbsItem href="/" label="Home" hasIcon>
              <Icon name="lucide:home" slot="icon" />
              <span slot="separator" class="separator" aria-hidden="true">
                <Icon name="lucide:chevron-right" />
              </span>
            </BreadcrumbsItem>
            <BreadcrumbsItem href="/docs" label="Documentation">
              <span slot="separator" class="separator" aria-hidden="true">
                <Icon name="lucide:chevron-right" />
              </span>
            </BreadcrumbsItem>
            <BreadcrumbsItem label="Breadcrumbs" currentPage={true} />
          </Breadcrumbs>
        </div>
        <div class="space-content">
          <h2>Cards</h2>
          <Card
            img="https://images.unsplash.com/photo-1620428268482-cf1851a36764?auto=format&fit=crop&q=100&w=800"
            title="Card Title"
            footer="Author name"
          >
            <span slot="meta">
              <Icon name="lucide:clock" />
              10 min read
            </span>
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo accusantium
            debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod veritatis laboriosam
            est tenetur.
          </Card>
        </div>
        <div class="space-content">
          <h2>Dark mode toggle</h2>
          <div class="flex gap-2">
            <DarkMode />
            <DarkMode>
              <Icon name="lucide:sun" slot="light" />
              <Icon name="lucide:moon" slot="dark" />
            </DarkMode>
          </div>
        </div>
        <div class="space-content">
          <h2>Media</h2>
          <Media
            class="rounded-lg"
            src="https://images.unsplash.com/photo-1620428268482-cf1851a36764?auto=format&fit=crop&q=100&w=800"
          />
        </div>
        <div class="space-content">
          <h2>Modals</h2>
          <button id="modal1-trigger" class="button">Modal 1</button>&nbsp;
          <button id="modal2-trigger" class="button color-secondary">Modal 2</button>
          <Modal triggerId="modal1-trigger" title="Modal 1">
            <p>Why hello, I be the <strong>first</strong> Modal.</p>
          </Modal>
          <Modal triggerId="modal2-trigger" title="Modal 2" closeText="Close Modal">
            <p>
              Ah yes, and I be the <strong>second</strong> Modal. Lorem ipsum dolor sit amet, consectetur adipisicing elit.
              Sed, a! Ratione eaque temporibus alias tempora pariatur dolorem.
            </p>
            <button class="button" onclick="closeModal()">Confirm action</button>
          </Modal>
        </div>
        <div class="space-content">
          <h2>Notification</h2>
          <Notification>
            <Icon name="lucide:bell" /><p><strong>Message:</strong> This is a notification!</p>
          </Notification>
          <Notification role="status" type="info">
            <Icon name="lucide:info" /><p>
              <strong>Info:</strong> This is a notification of type info.
            </p>
          </Notification>
          <Notification role="status" type="success">
            <Icon name="lucide:circle-check" /><p>
              <strong>Success:</strong> This is a notification of type success.
            </p>
          </Notification>
          <Notification role="status" type="warning">
            <Icon name="lucide:triangle-alert" /><p>
              <strong>Warning:</strong> This is a notification of type warning and goes on multiple lines to see how that
              looks.
            </p>
          </Notification>
          <Notification role="alert" type="error">
            <Icon name="lucide:circle-x" /><p>
              <strong>Error:</strong> This is a notification of type error.
            </p>
          </Notification>
        </div>
        <div class="space-content">
          <h2>Pagination</h2>
          <Pagination totalPages="200" />
        </div>
        <div class="space-content">
          <h2>Skip link</h2>
          <p>
            The SkipLink component becomes visible when you shift+tab to navigate backward through the page, it's the
            first focusable element on the page.
          </p>
        </div>
      </div>
      <div class="my-32"></div>
      <div class="space-content">
        <h2>Tabs</h2>
        <Tabs>
          <TabsList>
            <TabsTab id="tab-1" controls="panel-1" selected>First Panel</TabsTab>
            <TabsTab id="tab-2" controls="panel-2">Second Panel</TabsTab>
            <TabsTab id="tab-3" controls="panel-3">Third Panel</TabsTab>
          </TabsList>

          <TabsPanel id="panel-1" labelledby="tab-1" selected>
            <h3>First Panel</h3>
            <p>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
              accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
              veritatis laboriosam est tenetur. Lorem ipsum dolor, sit amet consectetur adipisicing elit. Sed eveniet
              quidem earum at nobis enim.
            </p>
            <a href="#">Tab to me!</a>
          </TabsPanel>
          <TabsPanel id="panel-2" labelledby="tab-2">
            <h3>Second Panel</h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
              <Media
                class="rounded-lg"
                src="https://images.unsplash.com/photo-1636819488537-a9b1ffb315ce?auto=format&fit=crop&w=800&q=100"
              />
              <Media
                class="rounded-lg"
                src="https://images.unsplash.com/photo-1636819488537-a9b1ffb315ce?auto=format&fit=crop&w=800&q=100"
              />
            </div>
            <p>
              Lorem ipsum dolor sit amet consectetur adipisicing elit. Architecto quasi nobis optio? Qui in quo
              accusantium debitis sapiente obcaecati magnam incidunt sit. Molestiae exercitationem quibusdam quod
              veritatis laboriosam est tenetur. Lorem ipsum dolor, sit amet consectetur adipisicing elit. Sed eveniet
              quidem earum at nobis enim.
            </p>
            <a href="#">Tab to me!</a>
          </TabsPanel>
          <TabsPanel id="panel-3" labelledby="tab-3">
            <h3>Third Panel</h3>
            <p>
              Lorem ipsum dolor sit amet consectetur, adipisicing elit. Tenetur, nulla! Sapiente saepe voluptate nemo in
              id aperiam tempore quo unde, ipsum similique explicabo non maiores qui voluptates dolore obcaecati. Atque
              voluptates similique ad ipsam nostrum quibusdam doloremque cum porro, aperiam beatae voluptas dolore et
              neque voluptate alias nesciunt blanditiis totam voluptatem necessitatibus. Similique aliquid molestiae
              iusto nam nobis ut aspernatur blanditiis provident iste corporis minima, quidem autem nulla doloremque
              eaque a obcaecati! Nisi quod quam repellendus facilis? Libero voluptas doloribus maxime, suscipit odio
              veritatis optio nulla officia quos cum at ea hic numquam perferendis molestiae! Aperiam quia veritatis
              earum tempora.
            </p>
            <a href="#">Tab to me!</a>
          </TabsPanel>
        </Tabs>
      </div>
    </div>
  </section>
  <section class="mt-32 mb-12">
    <div class="container">
      <div class="grid grid-cols-1 gap-32 md:grid-cols-2">
        <div class="space-content">
          <h2>Video (default)</h2>
          <Video src="https://www.youtube.com/watch?v=j8K-ESJF814" title="Video" />
        </div>
        <div class="space-content">
          <h2>Video (aspect ratio)</h2>
          <Video src="https://www.youtube.com/watch?v=j8K-ESJF814" title="Video" ratio="1:1" />
        </div>
      </div>
    </div>
  </section>
</DefaultLayout>
