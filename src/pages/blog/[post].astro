---
import DefaultLayout from "@layouts/DefaultLayout.astro"

export async function getStaticPaths() {
  // Get all blog posts from the content/blog directory
  const allPosts: any[] = await Astro.glob("../../../content/blog/**/index.md")

  return allPosts.map((post: any) => {
    // Extract slug from the file path
    const pathParts = post.file.split("/")
    const slug = pathParts[pathParts.length - 2]

    return {
      params: { post: slug },
      props: { post },
    }
  })
}

const { post } = Astro.props
const { Content } = post
const { title, date, description, image } = post.frontmatter

const author = {
  name: "<PERSON>",
  image: "/assets/profile-pic.png",
  bio: "Software developer who lives and works in Montreal, Canada 🍁",
}

// Helper function to format date
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })
}
---

<DefaultLayout title={title} description={description}>
  <article class="my-12">
    <div class="narrow container">
      <header class="mb-8">
        <h1 class="mb-4 text-4xl font-bold">{title}</h1>
        <div class="mb-4 text-gray-600">
          <time datetime={date}>{formatDate(date)}</time>
        </div>
        {image && <img src={image} alt={title} class="mb-6 h-64 w-full rounded-lg object-cover" />}
        {description && <p class="mb-6 text-xl text-gray-700">{description}</p>}
      </header>

      <div class="blog-content">
        <Content />
      </div>

      <footer class="mt-12 border-t border-gray-200 pt-8">
        <div class="flex items-center">
          <img src={author.image} alt={author.name} class="mr-4 h-12 w-12 rounded-full" />
          <div>
            <p class="font-semibold">{author.name}</p>
            <p class="text-sm text-gray-600">{author.bio}</p>
          </div>
        </div>
      </footer>
    </div>
  </article>
</DefaultLayout>

<style>
  .blog-content {
    max-width: none;
  }

  .blog-content h3 {
    color: var(--brand-primary, #ed1b2e);
    font-family:
      -apple-system,
      Segoe UI,
      Roboto,
      Noto Sans,
      Ubuntu,
      Cantarell,
      Helvetica Neue,
      sans-serif;
    letter-spacing: -0.01em;
    text-rendering: optimizeLegibility;
    text-transform: uppercase;
  }

  .blog-content code {
    border-radius: 0.25em;
    background-color: var(--brand-background-lightest, #f9f9f9);
    padding: 0.2em 0.4em;
    font-size: 1em;
  }

  .blog-content pre {
    position: relative;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
    background-color: #2d3748;
    padding: 1rem;
    overflow-x: auto;
    color: #e2e8f0;
  }

  .blog-content pre code {
    border-radius: 0;
    background-color: transparent;
    padding: 0;
    color: inherit;
  }

  @media (min-width: 600px) {
    .blog-content p,
    .blog-content li {
      font-size: 19px;
    }

    .blog-content h1 {
      font-size: 3.5rem;
    }
  }

  @media (min-width: 1100px) {
    .blog-content h1,
    .blog-content h2 {
      position: relative;
    }

    .blog-content h1::after {
      position: absolute;
      bottom: 0;
      left: -3em;
      background: var(--brand-background, #f3f7f9);
      width: calc(100% + 3em);
      height: 0.3em;
      content: "";
    }

    .blog-content h2::after {
      position: absolute;
      top: 0;
      bottom: 0;
      left: -3.5em;
      background: var(--brand-background, #f3f7f9);
      width: 3em;
      content: "";
    }
  }
</style>
